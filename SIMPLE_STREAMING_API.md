# Simplified Streaming Chat API

## Overview

The simplified streaming chat API at `/api/chat/message/stream` has been redesigned to remove complexity while maintaining core functionality. This new implementation focuses on simplicity and reliability.

## Key Simplifications

### 1. **Removed CSRF Protection**
- No more CSRF tokens required
- Eliminates token generation and validation complexity
- Reduces frontend integration complexity

### 2. **Simplified Session Handling**
- Automatic session ID generation for new conversations
- No complex session extraction middleware
- Clean session continuity using `sessionId` in request body

### 3. **Streamlined Guest User Support**
- No IP address tracking
- Simplified guest session management
- Automatic session creation

### 4. **Reduced Middleware Stack**
- Only essential middleware: `optionalAuth`, `validate`, `rateLimitPerUser`
- Removed: `extractSessionId`, `extractClientIP`, `CSRFProtection.protect()`

## API Endpoint

**POST** `/api/chat/message/stream`

### Headers
```
Content-Type: application/json
Authorization: Bearer <token> (optional - for authenticated users)
```

### Request Body
```json
{
  "message": "Your message here",
  "sessionId": "optional-session-id-for-conversation-continuity",
  "llmModel": "gpt-3.5-turbo"
}
```

### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `message` | string | Yes | The user's message (1-4000 characters) |
| `sessionId` | string | No | Session ID for conversation continuity |
| `llmModel` | string | No | LLM model to use (defaults to gpt-3.5-turbo) |

## Response Format

The API returns Server-Sent Events (SSE) stream with the following event types:

### Start Event
```json
{
  "type": "start",
  "message": "Response started",
  "metadata": {
    "sessionId": "abc123",
    "messageId": null,
    "isGuest": true,
    "userId": "user-id-if-authenticated",
    "chatId": "internal-chat-id"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Chunk Events
```json
{
  "type": "chunk",
  "content": "Partial response content...",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Complete Event
```json
{
  "type": "complete",
  "fullResponse": "Complete response text",
  "metadata": {
    "sessionId": "abc123",
    "messageId": "msg-uuid",
    "isGuest": true,
    "userId": "user-id-if-authenticated"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Event
```json
{
  "type": "error",
  "error": "Error message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Usage Examples

### New Conversation (No Session ID)
```javascript
const response = await fetch('/api/chat/message/stream', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    // 'Authorization': 'Bearer <token>' // Optional for authenticated users
  },
  body: JSON.stringify({
    message: "Hello, how are you?",
    llmModel: "gpt-3.5-turbo"
  })
});

const reader = response.body.getReader();
// Handle SSE stream...
```

### Continue Conversation (With Session ID)
```javascript
const response = await fetch('/api/chat/message/stream', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    message: "Tell me more about that",
    sessionId: "abc123", // From previous response
    llmModel: "gpt-3.5-turbo"
  })
});
```

## User Types

### Authenticated Users
- Must provide valid JWT token in Authorization header
- Credits are checked and deducted for each message
- Chats are associated with user ID
- Full conversation history and management

### Guest Users
- No authentication required
- No credit system
- Simplified session management
- Limited conversation history

## Chat Storage

### Database Configuration
- **Database**: `infini_ai_user_chat_recs`
- **Table**: `infini_ai_user_chat_recs.chat_messages`
- All chat messages are stored in the specified table for proper data organization

### For Authenticated Users
- Chats stored with `userId` association
- Proper thread continuity using `sessionId`
- Full message history preserved in `infini_ai_user_chat_recs.chat_messages`
- Chat titles auto-generated from first message

### For Guest Users
- Chats stored as guest sessions
- Session-based continuity
- Simplified storage without user association in `infini_ai_user_chat_recs.chat_messages`
- Auto-generated session IDs

## Error Handling

The API handles errors gracefully:

1. **Validation Errors**: Invalid message format, length, etc.
2. **Authentication Errors**: Invalid tokens (for authenticated users)
3. **Credit Errors**: Insufficient credits (for authenticated users)
4. **LLM Errors**: Model unavailable, API failures
5. **Database Errors**: Connection issues, storage failures

All errors are returned as SSE error events with descriptive messages.

## Rate Limiting

- 20 messages per minute per user/session
- Applied to both authenticated and guest users
- Prevents abuse and ensures fair usage

## Migration from Complex API

If migrating from the complex API:

1. **Remove CSRF token handling** from frontend
2. **Simplify session management** - just use sessionId from responses
3. **Remove IP-based logic** - no longer needed
4. **Update error handling** - simpler error format
5. **Test guest flows** - now much simpler

## Benefits

1. **Reduced Complexity**: Fewer middleware layers and simpler logic
2. **Better Reliability**: Less points of failure
3. **Easier Integration**: Simpler frontend requirements
4. **Improved Performance**: Reduced overhead
5. **Better Debugging**: Cleaner error paths and logging
