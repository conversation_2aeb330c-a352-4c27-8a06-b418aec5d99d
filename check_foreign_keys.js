/**
 * <PERSON><PERSON><PERSON> to check foreign key constraints on chat_messages table
 * Run with: node check_foreign_keys.js
 */

const { Sequelize } = require('sequelize');

// Database configuration
const chatDbConfig = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  port: parseInt(process.env.CHAT_DB_PORT || '3306'),
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  username: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
  password: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs',
};

async function checkForeignKeys() {
  console.log('🔍 Checking foreign key constraints...\n');

  const chatDatabase = new Sequelize(
    chatDbConfig.database,
    chatDbConfig.username,
    chatDbConfig.password,
    {
      host: chatDbConfig.host,
      port: chatDbConfig.port,
      dialect: 'mysql',
      logging: console.log,
    }
  );

  try {
    await chatDatabase.authenticate();
    console.log('✅ Connected to database\n');

    // Check foreign key constraints on chat_messages table
    console.log('🔍 Checking foreign key constraints on chat_messages table:');
    const [foreignKeys] = await chatDatabase.query(`
      SELECT
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = 'infini_ai_user_chat_recs'
      AND TABLE_NAME = 'chat_messages'
      AND REFERENCED_TABLE_NAME IS NOT NULL
    `);

    if (foreignKeys.length > 0) {
      console.log('📋 Found foreign key constraints:');
      console.table(foreignKeys);
      
      console.log('\n🔧 SQL commands to drop these constraints:');
      foreignKeys.forEach(fk => {
        console.log(`ALTER TABLE chat_messages DROP FOREIGN KEY ${fk.CONSTRAINT_NAME};`);
      });
    } else {
      console.log('✅ No foreign key constraints found on chat_messages table');
    }

    // Check what tables exist that could be referenced
    console.log('\n🔍 Checking related tables:');
    const [tables] = await chatDatabase.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'infini_ai_user_chat_recs' 
      AND TABLE_NAME IN ('chats', 'chat_threads')
      ORDER BY TABLE_NAME
    `);

    if (tables.length > 0) {
      console.log('📋 Related tables found:');
      console.table(tables);
    }

    // Check the structure of chat_messages table
    console.log('\n🔍 Chat messages table structure:');
    const [structure] = await chatDatabase.query(`DESCRIBE chat_messages`);
    console.table(structure);

    // Check if there are any records in the referenced tables
    console.log('\n📊 Record counts in related tables:');
    
    try {
      const [chatCount] = await chatDatabase.query(`SELECT COUNT(*) as count FROM chats`);
      console.log(`chats table: ${chatCount[0].count} records`);
    } catch (e) {
      console.log(`chats table: Error - ${e.message}`);
    }

    try {
      const [threadCount] = await chatDatabase.query(`SELECT COUNT(*) as count FROM chat_threads`);
      console.log(`chat_threads table: ${threadCount[0].count} records`);
    } catch (e) {
      console.log(`chat_threads table: Error - ${e.message}`);
    }

    try {
      const [messageCount] = await chatDatabase.query(`SELECT COUNT(*) as count FROM chat_messages`);
      console.log(`chat_messages table: ${messageCount[0].count} records`);
    } catch (e) {
      console.log(`chat_messages table: Error - ${e.message}`);
    }

  } catch (error) {
    console.error('❌ Check failed:', error.message);
  } finally {
    await chatDatabase.close();
    console.log('\n🔒 Database connection closed');
  }
}

// Run the check
checkForeignKeys().catch(console.error);
