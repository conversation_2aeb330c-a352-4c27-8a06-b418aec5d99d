/**
 * Quick script to check current thread names in the database
 * Run with: node check_thread_names.js
 */

const { Sequelize } = require('sequelize');

// Database configuration
const chatDbConfig = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  port: parseInt(process.env.CHAT_DB_PORT || '3306'),
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  username: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
  password: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs',
};

async function checkThreadNames() {
  console.log('🔍 Checking current thread names in database...\n');

  const chatDatabase = new Sequelize(
    chatDbConfig.database,
    chatDbConfig.username,
    chatDbConfig.password,
    {
      host: chatDbConfig.host,
      port: chatDbConfig.port,
      dialect: 'mysql',
      logging: false,
    }
  );

  try {
    await chatDatabase.authenticate();
    console.log('✅ Connected to infini_ai_user_chat_recs database\n');

    // Check all threads and their names
    const [threads] = await chatDatabase.query(`
      SELECT 
        id,
        LEFT(session_id, 12) as session_id,
        name,
        is_guest,
        user_id,
        created_at
      FROM chat_threads 
      ORDER BY created_at DESC
    `);

    console.log('📊 All threads in database:');
    if (threads.length > 0) {
      console.table(threads.map(thread => ({
        ID: thread.id.substring(0, 8) + '...',
        SessionID: thread.session_id + '...',
        Name: thread.name || 'NULL',
        IsGuest: thread.is_guest ? 'Yes' : 'No',
        UserID: thread.user_id ? thread.user_id.substring(0, 8) + '...' : 'N/A',
        CreatedAt: thread.created_at
      })));

      // Count threads with null names
      const nullNameCount = threads.filter(t => t.name === null).length;
      const totalCount = threads.length;
      
      console.log(`\n📈 Summary:`);
      console.log(`  Total threads: ${totalCount}`);
      console.log(`  Threads with NULL names: ${nullNameCount}`);
      console.log(`  Threads with names: ${totalCount - nullNameCount}`);
      
      if (nullNameCount > 0) {
        console.log(`\n⚠️  Found ${nullNameCount} threads with NULL names`);
        console.log(`   This indicates the bug was present before the fix`);
      } else {
        console.log(`\n✅ All threads have names - fix is working!`);
      }

      // Show some example names
      const namedThreads = threads.filter(t => t.name !== null);
      if (namedThreads.length > 0) {
        console.log(`\n📝 Example thread names:`);
        namedThreads.slice(0, 5).forEach((thread, index) => {
          console.log(`  ${index + 1}. "${thread.name}"`);
        });
      }

    } else {
      console.log('  No threads found in database');
    }

    // Also check if there are any messages for these threads
    console.log('\n💬 Checking messages for threads:');
    const [messageCount] = await chatDatabase.query(`
      SELECT COUNT(*) as count FROM chat_messages
    `);
    console.log(`  Total messages: ${messageCount[0].count}`);

    if (messageCount[0].count > 0) {
      const [recentMessages] = await chatDatabase.query(`
        SELECT 
          chat_id,
          LEFT(message, 30) as message_preview,
          created_at
        FROM chat_messages 
        ORDER BY created_at DESC 
        LIMIT 3
      `);
      
      console.log('\n📨 Recent messages:');
      console.table(recentMessages.map(msg => ({
        ThreadID: msg.chat_id.substring(0, 8) + '...',
        MessagePreview: msg.message_preview + '...',
        CreatedAt: msg.created_at
      })));
    }

  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  } finally {
    await chatDatabase.close();
    console.log('\n🔒 Database connection closed');
  }
}

// Run the check
checkThreadNames().catch(console.error);
