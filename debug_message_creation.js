/**
 * Debug script to test message creation directly
 * Run with: node debug_message_creation.js
 */

const { Sequelize, DataTypes } = require('sequelize');

// Database configuration
const chatDbConfig = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  port: parseInt(process.env.CHAT_DB_PORT || '3306'),
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  username: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
  password: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs',
};

const commonOptions = {
  dialect: 'mysql',
  logging: console.log, // Enable logging to see SQL queries
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
  },
};

// Simple UUID generator for testing
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function debugMessageCreation() {
  console.log('🔍 Debugging message creation process...\n');

  const chatDatabase = new Sequelize(
    chatDbConfig.database,
    chatDbConfig.username,
    chatDbConfig.password,
    {
      ...commonOptions,
      host: chatDbConfig.host,
      port: chatDbConfig.port,
    }
  );

  try {
    await chatDatabase.authenticate();
    console.log('✅ Connected to database\n');

    // Define ChatMessage model exactly as in the app
    const ChatMessage = chatDatabase.define('ChatMessage', {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        allowNull: false,
      },
      chatId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      response: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      llmModel: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      isUserMessage: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    }, {
      modelName: 'ChatMessage',
      tableName: 'chat_messages',
      timestamps: false,
    });

    // Get an existing thread ID to test with
    console.log('🔍 Finding existing thread...');
    const [threads] = await chatDatabase.query(`
      SELECT id, session_id, name FROM chat_threads ORDER BY created_at DESC LIMIT 1
    `);

    if (threads.length === 0) {
      console.log('❌ No threads found. Please create a thread first.');
      return;
    }

    const testThread = threads[0];
    console.log(`📋 Using thread: ${testThread.id} (${testThread.name})`);

    // Test message creation
    console.log('\n🧪 Testing message creation...');
    
    const testMessageData = {
      id: generateUUID(),
      chatId: testThread.id,
      message: 'Test message for debugging',
      response: 'Test response from LLM',
      llmModel: 'gpt-3.5-turbo',
      isUserMessage: true,
    };

    console.log('📝 Test message data:', testMessageData);

    try {
      console.log('\n🔄 Creating message...');
      const createdMessage = await ChatMessage.create(testMessageData);
      console.log('✅ Message created successfully!');
      console.log('📋 Created message:', {
        id: createdMessage.id,
        chatId: createdMessage.chatId,
        message: createdMessage.message.substring(0, 50) + '...',
        llmModel: createdMessage.llmModel,
        createdAt: createdMessage.createdAt
      });

      // Verify the message was stored
      console.log('\n🔍 Verifying message in database...');
      const [verifyMessages] = await chatDatabase.query(`
        SELECT id, chat_id, LEFT(message, 30) as message_preview, llm_model, created_at 
        FROM chat_messages 
        WHERE id = ?
      `, {
        replacements: [createdMessage.id]
      });

      if (verifyMessages.length > 0) {
        console.log('✅ Message verified in database:');
        console.table(verifyMessages);
      } else {
        console.log('❌ Message not found in database after creation');
      }

    } catch (createError) {
      console.error('❌ Error creating message:', createError.message);
      console.error('📋 Error details:', createError);
    }

    // Check current message count
    console.log('\n📊 Current message count in database:');
    const [messageCount] = await chatDatabase.query(`
      SELECT COUNT(*) as count FROM chat_messages
    `);
    console.log(`Total messages: ${messageCount[0].count}`);

    // Show recent messages
    const [recentMessages] = await chatDatabase.query(`
      SELECT id, chat_id, LEFT(message, 30) as message_preview, llm_model, created_at 
      FROM chat_messages 
      ORDER BY created_at DESC 
      LIMIT 5
    `);

    if (recentMessages.length > 0) {
      console.log('\n📨 Recent messages:');
      console.table(recentMessages);
    } else {
      console.log('\n📨 No messages found in database');
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('📋 Full error:', error);
  } finally {
    await chatDatabase.close();
    console.log('\n🔒 Database connection closed');
  }
}

// Run the debug
debugMessageCreation().catch(console.error);
