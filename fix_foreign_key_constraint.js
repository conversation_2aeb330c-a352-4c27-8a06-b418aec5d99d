/**
 * <PERSON><PERSON>t to fix the foreign key constraint issue
 * This will drop the foreign key constraint that's preventing message insertion
 * Run with: node fix_foreign_key_constraint.js
 */

const { Sequelize } = require('sequelize');

// Database configuration
const chatDbConfig = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  port: parseInt(process.env.CHAT_DB_PORT || '3306'),
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  username: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
  password: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs',
};

async function fixForeignKeyConstraint() {
  console.log('🔧 Fixing foreign key constraint issue...\n');

  const chatDatabase = new Sequelize(
    chatDbConfig.database,
    chatDbConfig.username,
    chatDbConfig.password,
    {
      host: chatDbConfig.host,
      port: chatDbConfig.port,
      dialect: 'mysql',
      logging: console.log,
    }
  );

  try {
    await chatDatabase.authenticate();
    console.log('✅ Connected to database\n');

    // First, check current foreign key constraints
    console.log('🔍 Current foreign key constraints:');
    const [foreignKeys] = await chatDatabase.query(`
      SELECT 
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = 'infini_ai_user_chat_recs' 
      AND TABLE_NAME = 'chat_messages' 
      AND REFERENCED_TABLE_NAME IS NOT NULL
    `);

    if (foreignKeys.length > 0) {
      console.table(foreignKeys);
      
      // Drop the foreign key constraint
      console.log('\n🔧 Dropping foreign key constraint...');
      await chatDatabase.query(`
        ALTER TABLE chat_messages DROP FOREIGN KEY chat_messages_ibfk_1
      `);
      console.log('✅ Foreign key constraint dropped successfully');
      
    } else {
      console.log('✅ No foreign key constraints found');
    }

    // Verify the constraint was dropped
    console.log('\n🔍 Verifying constraint removal...');
    const [remainingKeys] = await chatDatabase.query(`
      SELECT 
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = 'infini_ai_user_chat_recs' 
      AND TABLE_NAME = 'chat_messages' 
      AND REFERENCED_TABLE_NAME IS NOT NULL
    `);

    if (remainingKeys.length === 0) {
      console.log('✅ All foreign key constraints removed successfully');
    } else {
      console.log('⚠️  Some constraints still remain:');
      console.table(remainingKeys);
    }

    // Test message creation now
    console.log('\n🧪 Testing message creation after fix...');
    
    // Get a thread ID to test with
    const [threads] = await chatDatabase.query(`
      SELECT id FROM chat_threads ORDER BY created_at DESC LIMIT 1
    `);

    if (threads.length > 0) {
      const threadId = threads[0].id;
      console.log(`📋 Using thread ID: ${threadId}`);

      // Simple UUID generator
      function generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          const r = Math.random() * 16 | 0;
          const v = c == 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
      }

      const testMessageId = generateUUID();
      
      try {
        await chatDatabase.query(`
          INSERT INTO chat_messages (id, chat_id, message, response, llm_model, is_user_message, created_at)
          VALUES (?, ?, ?, ?, ?, ?, NOW())
        `, {
          replacements: [
            testMessageId,
            threadId,
            'Test message after foreign key fix',
            'Test response after foreign key fix',
            'gpt-3.5-turbo',
            true
          ]
        });
        
        console.log('✅ Test message created successfully!');
        
        // Verify the message was inserted
        const [testMessage] = await chatDatabase.query(`
          SELECT id, chat_id, LEFT(message, 30) as message_preview, created_at
          FROM chat_messages 
          WHERE id = ?
        `, {
          replacements: [testMessageId]
        });
        
        if (testMessage.length > 0) {
          console.log('✅ Test message verified in database:');
          console.table(testMessage);
        }
        
      } catch (insertError) {
        console.error('❌ Test message creation failed:', insertError.message);
      }
    } else {
      console.log('⚠️  No threads found to test with');
    }

    // Show final status
    console.log('\n📊 Final status:');
    const [messageCount] = await chatDatabase.query(`SELECT COUNT(*) as count FROM chat_messages`);
    console.log(`Messages in database: ${messageCount[0].count}`);

    console.log('\n🎉 Foreign key constraint fix completed!');
    console.log('💡 The streaming API should now be able to store messages correctly.');

  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    console.error('📋 Full error:', error);
  } finally {
    await chatDatabase.close();
    console.log('\n🔒 Database connection closed');
  }
}

// Run the fix
fixForeignKeyConstraint().catch(console.error);
