import { DataTypes, Model, Optional, Op } from 'sequelize';
import { chatDatabase } from '../../config/database';
import { ChatMessageAttributes } from '../../types';
import { Chat } from './Chat';
import { ChatThread } from './ChatThread';

interface ChatMessageCreationAttributes extends Optional<ChatMessageAttributes, 'id' | 'createdAt'> {}

export class ChatMessage extends Model<ChatMessageAttributes, ChatMessageCreationAttributes> implements ChatMessageAttributes {
  public id!: string;
  public chatId!: string;
  public message!: string;
  public response!: string;
  public llmModel!: string;
  public isUserMessage!: boolean;
  public readonly createdAt!: Date;

  // Static methods
  static async createMessage(data: {
    chatId: string;
    message: string;
    response: string;
    llmModel: string;
    isUserMessage?: boolean;
  }): Promise<ChatMessage> {
    const { EncryptionUtil } = await import('../../utils/encryption');

    return ChatMessage.create({
      id: EncryptionUtil.generateUUID(),
      chatId: data.chatId,
      message: data.message,
      response: data.response,
      llmModel: data.llmModel,
      isUserMessage: data.isUserMessage ?? true,
    });
  }

  static async findChatMessages(chatId: string, limit: number = 50, offset: number = 0): Promise<ChatMessage[]> {
    return ChatMessage.findAll({
      where: { chatId },
      order: [['createdAt', 'ASC']],
      limit,
      offset,
    });
  }

  static async findLatestMessage(chatId: string): Promise<ChatMessage | null> {
    return ChatMessage.findOne({
      where: { chatId },
      order: [['createdAt', 'DESC']],
    });
  }

  static async countChatMessages(chatId: string): Promise<number> {
    return ChatMessage.count({
      where: { chatId },
    });
  }

  static async deleteChatMessages(chatId: string): Promise<number> {
    return ChatMessage.destroy({
      where: { chatId },
    });
  }

  static async findMessagesByDateRange(
    chatId: string,
    startDate: Date,
    endDate: Date
  ): Promise<ChatMessage[]> {
    return ChatMessage.findAll({
      where: {
        chatId,
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
      order: [['createdAt', 'ASC']],
    });
  }
}

ChatMessage.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    chatId: {
      type: DataTypes.UUID,
      allowNull: false,
      // No foreign key constraint - application logic ensures referential integrity
      // This allows flexibility for both Chat and ChatThread IDs
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    response: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    llmModel: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    isUserMessage: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: chatDatabase,
    modelName: 'ChatMessage',
    tableName: 'chat_messages',
    timestamps: false,
    indexes: [
      {
        fields: ['chat_id'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['llm_model'],
      },
      {
        fields: ['is_user_message'],
      },
    ],
  }
);

// Define logical associations without foreign key constraints
// This prevents conflicts since ChatMessage can reference both Chat and ChatThread
Chat.hasMany(ChatMessage, {
  foreignKey: 'chat_id',
  as: 'messages',
  constraints: false // Disable foreign key constraint
});
ChatMessage.belongsTo(Chat, {
  foreignKey: 'chat_id',
  as: 'chat',
  constraints: false // Disable foreign key constraint
});

// Also support ChatThread associations
ChatThread.hasMany(ChatMessage, {
  foreignKey: 'chat_id',
  as: 'messages',
  constraints: false // Disable foreign key constraint
});
ChatMessage.belongsTo(ChatThread, {
  foreignKey: 'chat_id',
  as: 'thread',
  constraints: false // Disable foreign key constraint
});
