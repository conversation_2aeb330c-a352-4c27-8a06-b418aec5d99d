import { Router } from 'express';
import { AuthController } from '../controllers/AuthController';
import { validate, validationSchemas } from '../middleware/validation';
import { authenticateToken, rateLimitPerUser, extractSessionId } from '../middleware/auth';
import { CSRFProtection } from '../middleware/security';

const router = Router();

// Public routes (no authentication required)
router.post('/login',
  validate(validationSchemas.login),
  rateLimitPerUser(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  AuthController.login
);

router.post('/register',
  validate(validationSchemas.register),
  rateLimitPerUser(3, 60 * 60 * 1000), // 3 attempts per hour
  AuthController.register
);

router.post('/verify-registration',
  validate(validationSchemas.verifyRegistration),
  rateLimitPerUser(5, 15 * 60 * 1000), // 5 verification attempts per 15 minutes
  AuthController.verifyRegistration
);

router.post('/request-otp',
  validate(validationSchemas.requestOtp),
  rateLimitPerUser(3, 5 * 60 * 1000), // 3 OTP requests per 5 minutes
  AuthController.requestOTP
);

router.post('/verify-otp',
  validate(validationSchemas.verifyOtp),
  rateLimitPerUser(5, 15 * 60 * 1000), // 5 verification attempts per 15 minutes
  AuthController.verifyOTP
);

router.post('/resend-otp',
  validate(validationSchemas.requestOtp),
  rateLimitPerUser(2, 10 * 60 * 1000), // 2 resend attempts per 10 minutes
  AuthController.resendOTP
);

router.post('/verify-token',
  AuthController.verifyToken
);

router.get('/check-user',
  AuthController.checkUserExists
);

// Protected routes (authentication required)
router.post('/refresh-token',
  authenticateToken,
  AuthController.refreshToken
);

router.get('/profile',
  authenticateToken,
  AuthController.getProfile
);

router.post('/change-password',
  authenticateToken,
  validate(validationSchemas.changePassword),
  AuthController.changePassword
);

router.post('/logout',
  authenticateToken,
  AuthController.logout
);

// Admin routes (would typically require admin role)
router.get('/stats',
  authenticateToken,
  AuthController.getAuthStats
);

// CSRF token endpoint
router.get('/csrf-token',
  extractSessionId,
  CSRFProtection.getToken()
);

export default router;
