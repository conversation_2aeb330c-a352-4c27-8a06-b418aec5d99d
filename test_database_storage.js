/**
 * Test script to verify chat messages are stored in the correct table
 * Run with: node test_database_storage.js
 */

const { Sequelize } = require('sequelize');

// Chat Database Configuration
const chatDbConfig = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  port: parseInt(process.env.CHAT_DB_PORT || '3306'),
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  username: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
  password: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs',
};

const commonOptions = {
  dialect: 'mysql',
  logging: false, // Disable SQL logging for cleaner output
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
  },
};

async function testDatabaseStorage() {
  console.log('🧪 Testing database storage for chat messages...\n');

  const chatDatabase = new Sequelize(
    chatDbConfig.database,
    chatDbConfig.username,
    chatDbConfig.password,
    {
      ...commonOptions,
      host: chatDbConfig.host,
      port: chatDbConfig.port,
    }
  );

  try {
    await chatDatabase.authenticate();
    console.log('✅ Connected to infini_ai_user_chat_recs database\n');

    // Check both tables for existing messages
    console.log('📊 Checking message counts in both tables:\n');

    try {
      const [regularTable] = await chatDatabase.query('SELECT COUNT(*) as count FROM chat_messages');
      console.log(`📋 Messages in 'chat_messages': ${regularTable[0].count}`);
    } catch (error) {
      console.log(`❌ Error querying 'chat_messages': ${error.message}`);
    }

    try {
      const [qualifiedTable] = await chatDatabase.query('SELECT COUNT(*) as count FROM `infini_ai_user_chat_recs.chat_messages`');
      console.log(`📋 Messages in 'infini_ai_user_chat_recs.chat_messages': ${qualifiedTable[0].count}`);
    } catch (error) {
      console.log(`❌ Error querying 'infini_ai_user_chat_recs.chat_messages': ${error.message}`);
    }

    console.log('\n🔍 Recent messages from infini_ai_user_chat_recs.chat_messages:');
    try {
      const [recentMessages] = await chatDatabase.query(`
        SELECT id, LEFT(message, 50) as message_preview, llm_model, created_at 
        FROM \`infini_ai_user_chat_recs.chat_messages\` 
        ORDER BY created_at DESC 
        LIMIT 5
      `);
      
      if (recentMessages.length > 0) {
        console.table(recentMessages);
      } else {
        console.log('  No messages found');
      }
    } catch (error) {
      console.log(`❌ Error querying recent messages: ${error.message}`);
    }

    console.log('\n📝 Table structure comparison:\n');

    // Show structure of both tables
    try {
      console.log('🏗️  Structure of chat_messages:');
      const [regularStructure] = await chatDatabase.query('DESCRIBE chat_messages');
      console.table(regularStructure.map(col => ({
        Field: col.Field,
        Type: col.Type,
        Null: col.Null,
        Key: col.Key
      })));
    } catch (error) {
      console.log(`❌ Error describing chat_messages: ${error.message}`);
    }

    try {
      console.log('\n🏗️  Structure of infini_ai_user_chat_recs.chat_messages:');
      const [qualifiedStructure] = await chatDatabase.query('DESCRIBE `infini_ai_user_chat_recs.chat_messages`');
      console.table(qualifiedStructure.map(col => ({
        Field: col.Field,
        Type: col.Type,
        Null: col.Null,
        Key: col.Key
      })));
    } catch (error) {
      console.log(`❌ Error describing infini_ai_user_chat_recs.chat_messages: ${error.message}`);
    }

    console.log('\n💡 Configuration Summary:');
    console.log('  - Database: infini_ai_user_chat_recs');
    console.log('  - Target table: infini_ai_user_chat_recs.chat_messages');
    console.log('  - Model configured to use: infini_ai_user_chat_recs.chat_messages');
    console.log('\n✅ Database storage verification completed!');

  } catch (error) {
    console.error('❌ Database storage test failed:', error.message);
  } finally {
    await chatDatabase.close();
    console.log('\n🔒 Database connection closed');
  }
}

// Run the test
testDatabaseStorage().catch(console.error);
