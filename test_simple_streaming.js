/**
 * Simple test script for the new simplified streaming chat API
 * Run with: node test_simple_streaming.js
 */

const http = require('http');

const API_BASE = 'http://localhost:3000';

function testSimpleStreamingAPI() {
  console.log('🧪 Testing Simplified Streaming Chat API...\n');

  // Test data
  const testMessage = {
    message: "Hello! This is a test of the simplified streaming API.",
    llmModel: "gpt-3.5-turbo"
  };

  const postData = JSON.stringify(testMessage);

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/chat/message/stream',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  console.log('📤 Sending request to:', `${API_BASE}${options.path}`);
  console.log('📝 Request body:', testMessage);
  console.log('\n🔄 Streaming response:\n');

  const req = http.request(options, (res) => {
    console.log(`📊 Status: ${res.statusCode}`);
    console.log(`📋 Headers:`, res.headers);
    console.log('\n📡 Stream events:\n');

    let eventCount = 0;
    let sessionId = null;

    res.on('data', (chunk) => {
      const data = chunk.toString();
      const lines = data.split('\n');

      lines.forEach(line => {
        if (line.startsWith('data: ')) {
          try {
            const eventData = JSON.parse(line.substring(6));
            eventCount++;
            
            console.log(`Event ${eventCount}:`, eventData);
            
            // Extract session ID from first event for follow-up test
            if (eventData.metadata && eventData.metadata.sessionId) {
              sessionId = eventData.metadata.sessionId;
            }
            
          } catch (e) {
            console.log('Raw data:', line);
          }
        }
      });
    });

    res.on('end', () => {
      console.log('\n✅ Stream completed!');
      console.log(`📊 Total events received: ${eventCount}`);
      
      if (sessionId) {
        console.log(`🔗 Session ID: ${sessionId}`);
        console.log('\n🔄 Testing conversation continuity...\n');
        testContinuity(sessionId);
      } else {
        console.log('\n❌ No session ID received');
      }
    });

    res.on('error', (err) => {
      console.error('❌ Response error:', err);
    });
  });

  req.on('error', (err) => {
    console.error('❌ Request error:', err);
    console.log('\n💡 Make sure the server is running on port 3000');
    console.log('   Run: npm run dev');
  });

  req.write(postData);
  req.end();
}

function testContinuity(sessionId) {
  const followUpMessage = {
    message: "Can you tell me more about that?",
    sessionId: sessionId,
    llmModel: "gpt-3.5-turbo"
  };

  const postData = JSON.stringify(followUpMessage);

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/chat/message/stream',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  console.log('📤 Sending follow-up message...');
  console.log('📝 Request body:', followUpMessage);
  console.log('\n🔄 Follow-up response:\n');

  const req = http.request(options, (res) => {
    let eventCount = 0;

    res.on('data', (chunk) => {
      const data = chunk.toString();
      const lines = data.split('\n');

      lines.forEach(line => {
        if (line.startsWith('data: ')) {
          try {
            const eventData = JSON.parse(line.substring(6));
            eventCount++;
            console.log(`Follow-up Event ${eventCount}:`, eventData);
          } catch (e) {
            console.log('Raw data:', line);
          }
        }
      });
    });

    res.on('end', () => {
      console.log('\n✅ Conversation continuity test completed!');
      console.log(`📊 Total follow-up events: ${eventCount}`);
      console.log('\n🎉 All tests completed successfully!');
    });

    res.on('error', (err) => {
      console.error('❌ Follow-up response error:', err);
    });
  });

  req.on('error', (err) => {
    console.error('❌ Follow-up request error:', err);
  });

  req.write(postData);
  req.end();
}

async function verifyDatabaseStorage() {
  console.log('\n🗄️  Verifying database storage...');

  const { Sequelize } = require('sequelize');

  const chatDatabase = new Sequelize(
    'infini_ai_user_chat_recs',
    'inf_ai_chat_recs',
    'inf_ai_chat_recs',
    {
      host: 'localhost',
      port: 3306,
      dialect: 'mysql',
      logging: false,
    }
  );

  try {
    await chatDatabase.authenticate();

    const [messages] = await chatDatabase.query(`
      SELECT COUNT(*) as count
      FROM chat_messages
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
    `);

    console.log(`📊 Recent messages in target table: ${messages[0].count}`);
    console.log(`📍 Database context: ${chatDatabase.getDatabaseName()}`);

    if (messages[0].count > 0) {
      console.log('✅ Messages are being stored in infini_ai_user_chat_recs.chat_messages');
    } else {
      console.log('ℹ️  No recent messages found (test may not have completed yet)');
    }

  } catch (error) {
    console.log(`❌ Database verification error: ${error.message}`);
  } finally {
    await chatDatabase.close();
  }
}

// Run the test
testSimpleStreamingAPI();

// Add a delay and then verify database storage
setTimeout(verifyDatabaseStorage, 10000); // Wait 10 seconds after test starts
