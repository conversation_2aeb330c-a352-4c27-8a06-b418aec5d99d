/**
 * Test script to verify thread names are being set correctly
 * Run with: node test_thread_names.js
 */

const http = require('http');
const { Sequelize } = require('sequelize');

const API_BASE = 'http://localhost:3000';

// Database configuration
const chatDbConfig = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  port: parseInt(process.env.CHAT_DB_PORT || '3306'),
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  username: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
  password: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs',
};

function testThreadNameAPI() {
  console.log('🧪 Testing Thread Name Generation...\n');

  // Test data with a specific message to check name generation
  const testMessage = {
    message: "Hello, this is a test message for thread name generation!",
    llmModel: "gpt-3.5-turbo"
  };

  const postData = JSON.stringify(testMessage);

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/chat/message/stream',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  console.log('📤 Sending request to:', `${API_BASE}${options.path}`);
  console.log('📝 Test message:', testMessage.message);
  console.log('📏 Expected thread name: "Hello, thi..." (first 10 chars + ...)');
  console.log('\n🔄 Streaming response:\n');

  const req = http.request(options, (res) => {
    console.log(`📊 Status: ${res.statusCode}`);

    let sessionId = null;
    let threadId = null;

    res.on('data', (chunk) => {
      const data = chunk.toString();
      const lines = data.split('\n');

      lines.forEach(line => {
        if (line.startsWith('data: ')) {
          try {
            const eventData = JSON.parse(line.substring(6));
            
            // Extract session ID and thread ID from metadata
            if (eventData.metadata) {
              if (eventData.metadata.sessionId) {
                sessionId = eventData.metadata.sessionId;
              }
              if (eventData.metadata.threadId) {
                threadId = eventData.metadata.threadId;
              }
            }
            
            if (eventData.type === 'complete') {
              console.log('✅ Stream completed!');
              console.log(`🔗 Session ID: ${sessionId}`);
              console.log(`🧵 Thread ID: ${threadId}`);
            }
            
          } catch (e) {
            // Ignore parsing errors for non-JSON lines
          }
        }
      });
    });

    res.on('end', () => {
      console.log('\n🔍 Verifying thread name in database...');
      if (threadId) {
        verifyThreadName(threadId, testMessage.message);
      } else {
        console.log('❌ No thread ID received');
      }
    });

    res.on('error', (err) => {
      console.error('❌ Response error:', err);
    });
  });

  req.on('error', (err) => {
    console.error('❌ Request error:', err);
    console.log('\n💡 Make sure the server is running on port 3000');
    console.log('   Run: npm run dev');
  });

  req.write(postData);
  req.end();
}

async function verifyThreadName(threadId, originalMessage) {
  const chatDatabase = new Sequelize(
    chatDbConfig.database,
    chatDbConfig.username,
    chatDbConfig.password,
    {
      host: chatDbConfig.host,
      port: chatDbConfig.port,
      dialect: 'mysql',
      logging: false,
    }
  );

  try {
    await chatDatabase.authenticate();
    
    // Query the thread to check its name
    const [threads] = await chatDatabase.query(`
      SELECT id, session_id, name, is_guest, user_id, created_at 
      FROM chat_threads 
      WHERE id = ?
    `, {
      replacements: [threadId]
    });
    
    if (threads.length > 0) {
      const thread = threads[0];
      console.log('\n📋 Thread Details:');
      console.table([{
        ID: thread.id,
        SessionID: thread.session_id,
        Name: thread.name,
        IsGuest: thread.is_guest ? 'Yes' : 'No',
        UserID: thread.user_id || 'N/A',
        CreatedAt: thread.created_at
      }]);
      
      // Verify the name generation logic
      const expectedName = originalMessage.substring(0, 10).trim() + (originalMessage.length > 10 ? '...' : '');
      
      console.log('\n🔍 Name Verification:');
      console.log(`📝 Original message: "${originalMessage}"`);
      console.log(`📏 Expected name: "${expectedName}"`);
      console.log(`💾 Actual name: "${thread.name}"`);
      
      if (thread.name === expectedName) {
        console.log('✅ Thread name is correct!');
      } else if (thread.name === null) {
        console.log('❌ Thread name is NULL - this is the bug we fixed');
      } else {
        console.log('⚠️  Thread name doesn\'t match expected format');
      }
      
    } else {
      console.log('❌ Thread not found in database');
    }
    
    // Show recent threads for comparison
    console.log('\n📊 Recent threads in database:');
    const [recentThreads] = await chatDatabase.query(`
      SELECT id, LEFT(session_id, 8) as session_id, name, is_guest, created_at 
      FROM chat_threads 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    if (recentThreads.length > 0) {
      console.table(recentThreads);
    } else {
      console.log('  No threads found');
    }
    
  } catch (error) {
    console.log(`❌ Database verification error: ${error.message}`);
  } finally {
    await chatDatabase.close();
    console.log('\n🔒 Database connection closed');
    console.log('\n🎉 Thread name test completed!');
  }
}

// Run the test
testThreadNameAPI();
