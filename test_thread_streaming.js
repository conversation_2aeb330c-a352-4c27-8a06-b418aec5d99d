/**
 * Test script for thread streaming endpoint
 * Run with: node test_thread_streaming.js
 */

const http = require('http');

const API_BASE = 'http://localhost:5529';

function testThreadStreaming() {
  console.log('🧪 Testing Thread Streaming API...\n');

  // Test data - using the format you provided
  const testMessage = {
    message: "describe LLMs in brief",
    llmModel: "gpt-3.5-turbo",
    threadId: "e33b4eca-71a3-4a48-8ee6-fae771bb7e71"
    // Note: sessionId removed as it's not allowed in thread schema
  };

  const postData = JSON.stringify(testMessage);

  const options = {
    hostname: 'localhost',
    port: 5529,
    path: '/api/threads/message/stream',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      // Note: You'll need to add your actual JWT token here
      'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE'
    }
  };

  console.log('📤 Sending request to:', `${API_BASE}${options.path}`);
  console.log('📝 Request body:', testMessage);
  console.log('⚠️  Note: You need to replace YOUR_JWT_TOKEN_HERE with an actual JWT token');
  console.log('\n🔄 Streaming response:\n');

  const req = http.request(options, (res) => {
    console.log(`📊 Status: ${res.statusCode}`);
    console.log(`📋 Headers:`, res.headers);
    console.log('\n📡 Stream events:\n');

    let eventCount = 0;
    let threadId = null;

    res.on('data', (chunk) => {
      const data = chunk.toString();
      const lines = data.split('\n');

      lines.forEach(line => {
        if (line.startsWith('data: ')) {
          try {
            const eventData = JSON.parse(line.substring(6));
            eventCount++;
            
            console.log(`Event ${eventCount}:`, eventData);
            
            // Extract thread ID from metadata
            if (eventData.metadata && eventData.metadata.threadId) {
              threadId = eventData.metadata.threadId;
            }
            
          } catch (e) {
            console.log('Raw data:', line);
          }
        }
      });
    });

    res.on('end', () => {
      console.log('\n✅ Stream completed!');
      console.log(`📊 Total events received: ${eventCount}`);
      
      if (threadId) {
        console.log(`🧵 Thread ID: ${threadId}`);
      }
    });

    res.on('error', (err) => {
      console.error('❌ Response error:', err);
    });
  });

  req.on('error', (err) => {
    console.error('❌ Request error:', err);
    
    if (err.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the server is running on port 5529');
      console.log('   Run: npm run dev');
    }
  });

  req.write(postData);
  req.end();
}

function testWithoutAuth() {
  console.log('\n🧪 Testing without authentication (should fail)...\n');

  const testMessage = {
    message: "test message",
    llmModel: "gpt-3.5-turbo"
  };

  const postData = JSON.stringify(testMessage);

  const options = {
    hostname: 'localhost',
    port: 5529,
    path: '/api/threads/message/stream',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
      // No Authorization header
    }
  };

  const req = http.request(options, (res) => {
    console.log(`📊 Status: ${res.statusCode}`);
    
    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk.toString();
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('📋 Response:', response);
        
        if (res.statusCode === 401) {
          console.log('✅ Correctly rejected unauthorized request');
        } else {
          console.log('⚠️  Unexpected response for unauthorized request');
        }
      } catch (e) {
        console.log('📋 Raw response:', responseData);
      }
    });
  });

  req.on('error', (err) => {
    console.error('❌ Request error:', err);
  });

  req.write(postData);
  req.end();
}

console.log('🔧 Thread Streaming API Test\n');
console.log('This test will check:');
console.log('1. ✅ CSRF protection removed from streaming endpoint');
console.log('2. ✅ Authentication still required');
console.log('3. ✅ Validation accepts threadId (not sessionId)');
console.log('4. ✅ Streaming response works correctly\n');

// Run the tests
testThreadStreaming();

// Test without auth after a delay
setTimeout(testWithoutAuth, 2000);
