/**
 * Verification script to confirm chat messages are stored in the correct database
 * This simulates the Sequelize model behavior to verify the configuration
 */

const { Sequelize, DataTypes } = require('sequelize');

// Chat Database Configuration (same as in src/config/database.ts)
const chatDbConfig = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  port: parseInt(process.env.CHAT_DB_PORT || '3306'),
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  username: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
  password: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs',
};

const commonOptions = {
  dialect: 'mysql',
  logging: console.log, // Enable logging to see SQL queries
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
  },
};

async function verifyCorrectStorage() {
  console.log('🔍 Verifying correct database storage configuration...\n');

  // Create the same database connection as the app
  const chatDatabase = new Sequelize(
    chatDbConfig.database,
    chatDbConfig.username,
    chatDbConfig.password,
    {
      ...commonOptions,
      host: chatDbConfig.host,
      port: chatDbConfig.port,
    }
  );

  try {
    // Test connection
    console.log('📡 Testing database connection...');
    await chatDatabase.authenticate();
    console.log(`✅ Connected to database: ${chatDbConfig.database}\n`);

    // Define the ChatMessage model exactly as in the app
    const ChatMessage = chatDatabase.define('ChatMessage', {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        allowNull: false,
      },
      chatId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      response: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      llmModel: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      isUserMessage: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    }, {
      modelName: 'ChatMessage',
      tableName: 'chat_messages', // This is the key - just the table name
      timestamps: false,
      indexes: [
        {
          fields: ['chat_id'],
        },
        {
          fields: ['created_at'],
        },
        {
          fields: ['llm_model'],
        },
        {
          fields: ['is_user_message'],
        },
      ],
    });

    // Show what database and table will be used
    console.log('📋 Model Configuration:');
    console.log(`  - Database: ${chatDatabase.getDatabaseName()}`);
    console.log(`  - Table: ${ChatMessage.tableName}`);
    console.log(`  - Full path: ${chatDatabase.getDatabaseName()}.${ChatMessage.tableName}`);
    console.log('');

    // Verify this matches our target
    const expectedDatabase = 'infini_ai_user_chat_recs';
    const expectedTable = 'chat_messages';
    const fullExpectedPath = `${expectedDatabase}.${expectedTable}`;

    if (chatDatabase.getDatabaseName() === expectedDatabase && ChatMessage.tableName === expectedTable) {
      console.log(`✅ CORRECT: Messages will be stored in ${fullExpectedPath}`);
    } else {
      console.log(`❌ INCORRECT: Expected ${fullExpectedPath}, but got ${chatDatabase.getDatabaseName()}.${ChatMessage.tableName}`);
    }

    // Test sync (this will create the table if it doesn't exist)
    console.log('\n🔄 Testing table sync...');
    await ChatMessage.sync({ alter: true });
    console.log('✅ Table sync successful');

    // Verify the table exists in the correct database
    console.log('\n🔍 Verifying table exists in correct database...');
    const [results] = await chatDatabase.query(`
      SELECT TABLE_SCHEMA, TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = '${expectedDatabase}' 
      AND TABLE_NAME = '${expectedTable}'
    `);

    if (results.length > 0) {
      console.log(`✅ Confirmed: Table ${expectedTable} exists in database ${expectedDatabase}`);
      console.log(`📊 Table details:`, results[0]);
    } else {
      console.log(`❌ Table ${expectedTable} not found in database ${expectedDatabase}`);
    }

    console.log('\n🎉 Verification completed successfully!');
    console.log('\n💡 Summary:');
    console.log(`  - The ChatMessage model is correctly configured`);
    console.log(`  - Messages will be stored in: ${expectedDatabase}.${expectedTable}`);
    console.log(`  - This matches the requirement to use infini_ai_user_chat_recs.chat_messages`);

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    
    if (error.original && error.original.code === 'ER_PARSE_ERROR') {
      console.log('\n💡 SQL syntax error detected. This suggests an issue with table/index naming.');
    }
  } finally {
    await chatDatabase.close();
    console.log('\n🔒 Database connection closed');
  }
}

// Run verification
verifyCorrectStorage().catch(console.error);
