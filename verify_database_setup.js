/**
 * Database verification script
 * Run with: node verify_database_setup.js
 */

const { Sequelize } = require('sequelize');

// Chat Database Configuration (same as in src/config/database.ts)
const chatDbConfig = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  port: parseInt(process.env.CHAT_DB_PORT || '3306'),
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  username: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
  password: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs',
};

const commonOptions = {
  dialect: 'mysql',
  logging: console.log,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
  },
};

async function verifyDatabaseSetup() {
  console.log('🔍 Verifying database setup...\n');

  const chatDatabase = new Sequelize(
    chatDbConfig.database,
    chatDbConfig.username,
    chatDbConfig.password,
    {
      ...commonOptions,
      host: chatDbConfig.host,
      port: chatDbConfig.port,
    }
  );

  try {
    // Test connection
    console.log('📡 Testing database connection...');
    await chatDatabase.authenticate();
    console.log('✅ Connection to infini_ai_user_chat_recs database successful\n');

    // Check if chat_messages table exists
    console.log('🔍 Checking if chat_messages table exists...');
    const [results] = await chatDatabase.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'infini_ai_user_chat_recs' 
      AND TABLE_NAME = 'chat_messages'
    `);

    if (results.length > 0) {
      console.log('✅ chat_messages table exists in infini_ai_user_chat_recs database\n');
      
      // Show table structure
      console.log('📋 Table structure:');
      const [columns] = await chatDatabase.query(`
        DESCRIBE infini_ai_user_chat_recs.chat_messages
      `);
      console.table(columns);
      
    } else {
      console.log('❌ chat_messages table does not exist in infini_ai_user_chat_recs database');
      console.log('💡 The table will be created when the application starts and syncs the database\n');
    }

    // Show current database
    console.log('🗄️  Current database context:');
    const [dbResult] = await chatDatabase.query('SELECT DATABASE() as current_db');
    console.log('Current database:', dbResult[0].current_db);

    // List all tables in the database
    console.log('\n📚 All tables in infini_ai_user_chat_recs:');
    const [tables] = await chatDatabase.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'infini_ai_user_chat_recs'
      ORDER BY TABLE_NAME
    `);
    
    if (tables.length > 0) {
      tables.forEach(table => {
        console.log(`  - ${table.TABLE_NAME}`);
      });
    } else {
      console.log('  No tables found (will be created on first app start)');
    }

  } catch (error) {
    console.error('❌ Database verification failed:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Access denied. Please check:');
      console.log('  - Database credentials in .env file');
      console.log('  - User permissions for inf_ai_chat_recs user');
      console.log('  - Run: mysql -u root -p < setup-database.sql');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Connection refused. Please check:');
      console.log('  - MySQL server is running');
      console.log('  - Host and port configuration');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 Database does not exist. Please run:');
      console.log('  mysql -u root -p < setup-database.sql');
    }
  } finally {
    await chatDatabase.close();
    console.log('\n🔒 Database connection closed');
  }
}

// Run verification
verifyDatabaseSetup().catch(console.error);
