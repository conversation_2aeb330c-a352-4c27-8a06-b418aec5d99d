/**
 * Verification script to confirm chat threads are stored in the correct table
 * Run with: node verify_thread_storage.js
 */

const { Sequelize } = require('sequelize');

// Chat Database Configuration
const chatDbConfig = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  port: parseInt(process.env.CHAT_DB_PORT || '3306'),
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  username: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
  password: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs',
};

const commonOptions = {
  dialect: 'mysql',
  logging: false, // Disable SQL logging for cleaner output
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
  },
};

async function verifyThreadStorage() {
  console.log('🧪 Verifying chat thread storage configuration...\n');

  const chatDatabase = new Sequelize(
    chatDbConfig.database,
    chatDbConfig.username,
    chatDbConfig.password,
    {
      ...commonOptions,
      host: chatDbConfig.host,
      port: chatDbConfig.port,
    }
  );

  try {
    await chatDatabase.authenticate();
    console.log('✅ Connected to infini_ai_user_chat_recs database\n');

    // Check both tables for existing data
    console.log('📊 Checking data in both tables:\n');

    try {
      const [chatsTable] = await chatDatabase.query('SELECT COUNT(*) as count FROM chats');
      console.log(`📋 Records in 'chats' table: ${chatsTable[0].count}`);
    } catch (error) {
      console.log(`❌ Error querying 'chats' table: ${error.message}`);
    }

    try {
      const [threadsTable] = await chatDatabase.query('SELECT COUNT(*) as count FROM chat_threads');
      console.log(`📋 Records in 'chat_threads' table: ${threadsTable[0].count}`);
    } catch (error) {
      console.log(`❌ Error querying 'chat_threads' table: ${error.message}`);
    }

    try {
      const [messagesTable] = await chatDatabase.query('SELECT COUNT(*) as count FROM chat_messages');
      console.log(`📋 Records in 'chat_messages' table: ${messagesTable[0].count}`);
    } catch (error) {
      console.log(`❌ Error querying 'chat_messages' table: ${error.message}`);
    }

    console.log('\n🔍 Recent threads from chat_threads:');
    try {
      const [recentThreads] = await chatDatabase.query(`
        SELECT id, session_id, name, is_guest, user_id, created_at 
        FROM chat_threads 
        ORDER BY created_at DESC 
        LIMIT 5
      `);
      
      if (recentThreads.length > 0) {
        console.table(recentThreads);
      } else {
        console.log('  No threads found');
      }
    } catch (error) {
      console.log(`❌ Error querying recent threads: ${error.message}`);
    }

    console.log('\n🔍 Recent chats from chats table:');
    try {
      const [recentChats] = await chatDatabase.query(`
        SELECT id, session_id, title, is_guest, user_id, created_at 
        FROM chats 
        ORDER BY created_at DESC 
        LIMIT 5
      `);
      
      if (recentChats.length > 0) {
        console.table(recentChats);
      } else {
        console.log('  No chats found');
      }
    } catch (error) {
      console.log(`❌ Error querying recent chats: ${error.message}`);
    }

    console.log('\n📝 Table structure comparison:\n');

    // Show structure of both tables
    try {
      console.log('🏗️  Structure of chats table:');
      const [chatsStructure] = await chatDatabase.query('DESCRIBE chats');
      console.table(chatsStructure.map(col => ({
        Field: col.Field,
        Type: col.Type,
        Null: col.Null,
        Key: col.Key
      })));
    } catch (error) {
      console.log(`❌ Error describing chats table: ${error.message}`);
    }

    try {
      console.log('\n🏗️  Structure of chat_threads table:');
      const [threadsStructure] = await chatDatabase.query('DESCRIBE chat_threads');
      console.table(threadsStructure.map(col => ({
        Field: col.Field,
        Type: col.Type,
        Null: col.Null,
        Key: col.Key
      })));
    } catch (error) {
      console.log(`❌ Error describing chat_threads table: ${error.message}`);
    }

    console.log('\n💡 Expected Behavior After Fix:');
    console.log('  ✅ New streaming API calls should create records in chat_threads table');
    console.log('  ✅ Messages should be stored in chat_messages table with thread IDs');
    console.log('  ❌ New streaming API calls should NOT create records in chats table');
    console.log('\n📊 Key Differences:');
    console.log('  - chats table: has "title" field, used by old Chat model');
    console.log('  - chat_threads table: has "name" and "project_id" fields, used by ChatThread model');

    console.log('\n✅ Thread storage verification completed!');

  } catch (error) {
    console.error('❌ Thread storage verification failed:', error.message);
  } finally {
    await chatDatabase.close();
    console.log('\n🔒 Database connection closed');
  }
}

// Run the verification
verifyThreadStorage().catch(console.error);
